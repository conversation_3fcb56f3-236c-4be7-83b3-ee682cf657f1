{"rustc": 16006996104574632743, "features": "[\"borsh\"]", "declared_features": "[\"borsh\", \"serde-traits\"]", "target": 1586917627076750936, "profile": 15657897354478470176, "path": 16001608697859263231, "deps": [[761872526999005629, "solana_msg", false, 764072810218005281], [5157631553186200874, "num_traits", false, 14567187325378532629], [7718779164700081417, "borsh", false, 43247154436677967], [8904325910339146545, "solana_decode_error", false, 15870537064508360090], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [10806645703491011684, "thiserror", false, 4716163417999155942], [11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [11263754829263059703, "num_derive", false, 10303754624925241684], [11330212578939379604, "solana_program_error", false, 3336987160806504165], [13076440941637918287, "bytemuck_derive", false, 1978016075602608915], [13740259144933249371, "solana_program_option", false, 17391663759309791004], [14074610438553418890, "bytemuck", false, 504061773874182866]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-pod-78ae01d71b41f236/dep-lib-spl_pod", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}