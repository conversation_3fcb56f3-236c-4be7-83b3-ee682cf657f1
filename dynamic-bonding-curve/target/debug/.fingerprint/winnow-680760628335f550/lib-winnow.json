{"rustc": 16006996104574632743, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 15644445642383851471, "path": 18152484256745330561, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-680760628335f550/dep-lib-winnow", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}