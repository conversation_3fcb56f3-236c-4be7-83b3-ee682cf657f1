{"rustc": 16006996104574632743, "features": "[\"convert\", \"heck\", \"sha2\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 2225463790103693989, "path": 4152399251661079441, "deps": [[5236433071915784494, "sha2", false, 3800606140697423872], [9689903380558560274, "serde", false, 3711773370135953871], [15367738274754116744, "serde_json", false, 5360447862243250299], [16131248048418321657, "heck", false, 775254493768138390], [17037804673887881428, "anchor_lang_idl_spec", false, 1016809099000504103], [18396545099218507036, "anyhow", false, 1697364170871226896]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-idl-65fae56a94d0d4fb/dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}