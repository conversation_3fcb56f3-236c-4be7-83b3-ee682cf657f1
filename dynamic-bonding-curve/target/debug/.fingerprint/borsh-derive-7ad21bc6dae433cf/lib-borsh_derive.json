{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 3986814255933454213, "profile": 2225463790103693989, "path": 5916482088795543135, "deps": [[256551579767560629, "proc_macro_crate", false, 1387202039115244694], [2713742371683562785, "syn", false, 16295224067803507893], [3060637413840920116, "proc_macro2", false, 1276041253075378396], [7776739900288582982, "borsh_derive_internal", false, 13537659809862048200], [13477581923615757443, "borsh_schema_derive_internal", false, 13036691924072627912]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-derive-7ad21bc6dae433cf/dep-lib-borsh_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}