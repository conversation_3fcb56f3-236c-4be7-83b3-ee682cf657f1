{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 13909130799607605443, "profile": 15657897354478470176, "path": 8400591228167095258, "deps": [[730322694175296062, "solana_address_lookup_table_interface", false, 18134057943861976890], [1461800729938538396, "solana_instruction", false, 7425754530109857019], [4043952427700520164, "solana_nonce", false, 15064970027818316465], [7355047358885037824, "solana_hash", false, 12443036087723803351], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9689903380558560274, "serde", false, 3711773370135953871], [10806645703491011684, "thiserror", false, 4716163417999155942], [11087274787214030812, "solana_keccak_hasher", false, 13841536996051084925], [12564212015386640564, "solana_clock", false, 17042852909674836676], [14591356476411885690, "solana_sdk_ids", false, 6479576252039189143], [15341883195918613377, "solana_system_interface", false, 3700732144194427837], [16257276029081467297, "serde_derive", false, 13597335082114753276], [17020231746412854753, "solana_message", false, 11900035230390590172]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-example-mocks-7c646f30d08aa7c6/dep-lib-solana_example_mocks", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}