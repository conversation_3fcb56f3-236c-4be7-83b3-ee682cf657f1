{"rustc": 16006996104574632743, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 600225532800476200, "profile": 2225463790103693989, "path": 12089135108133927959, "deps": [[3060637413840920116, "proc_macro2", false, 1276041253075378396], [7343171641404694157, "syn", false, 9770868895459145944], [15203748914246919255, "proc_macro_crate", false, 14043805755272569032], [16437840124237027127, "quote", false, 890238129459630126]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/num_enum_derive-e43c3fc7a00297ba/dep-lib-num_enum_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}