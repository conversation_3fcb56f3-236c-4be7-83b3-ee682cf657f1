{"rustc": 16006996104574632743, "features": "[\"borsh\", \"bytemuck\", \"serde\", \"std\"]", "declared_features": "[\"borsh\", \"bytemuck\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\", \"std\"]", "target": 14600528003313624360, "profile": 6652793396284126538, "path": 17157524444264658244, "deps": [[6616501577376279788, "bs58", false, 18386691801614988368], [7718779164700081417, "borsh", false, 43247154436677967], [9689903380558560274, "serde", false, 3711773370135953871], [13076440941637918287, "bytemuck_derive", false, 1978016075602608915], [14074610438553418890, "bytemuck", false, 504061773874182866], [14254950316256772154, "solana_atomic_u64", false, 18048900424764280692], [15429715045911386410, "solana_sanitize", false, 5311198693155383094], [16257276029081467297, "serde_derive", false, 13597335082114753276]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-hash-7a5ce5f84295eebb/dep-lib-solana_hash", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}