{"rustc": 16006996104574632743, "features": "[\"default\", \"schema\"]", "declared_features": "[\"default\", \"force_exhaustive_checks\", \"schema\"]", "target": 18019366223131144178, "profile": 2225463790103693989, "path": 831182968245674529, "deps": [[1213962973451362254, "once_cell", false, 13899380075162517159], [3060637413840920116, "proc_macro2", false, 1276041253075378396], [7343171641404694157, "syn", false, 9770868895459145944], [15203748914246919255, "proc_macro_crate", false, 14043805755272569032], [16437840124237027127, "quote", false, 890238129459630126]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-derive-a0bc5860dd04243f/dep-lib-borsh_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}