{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 5107985195345968876, "profile": 15657897354478470176, "path": 7216168054121312219, "deps": [[1461800729938538396, "solana_instruction", false, 7425754530109857019], [9475981483390999469, "solana_pubkey", false, 8221103193541484978]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-associated-token-account-client-cb8e76b519d5d819/dep-lib-spl_associated_token_account_client", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}