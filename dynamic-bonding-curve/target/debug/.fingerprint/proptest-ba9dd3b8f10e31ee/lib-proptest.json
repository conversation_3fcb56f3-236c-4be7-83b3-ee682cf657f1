{"rustc": 16006996104574632743, "features": "[\"bit-set\", \"default\", \"fork\", \"lazy_static\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\"]", "declared_features": "[\"alloc\", \"atomic64bit\", \"attr-macro\", \"bit-set\", \"default\", \"default-code-coverage\", \"fork\", \"handle-panics\", \"hardware-rng\", \"lazy_static\", \"no_std\", \"proptest-macro\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\", \"unstable\", \"x86\"]", "target": 7997875189344830950, "profile": 15657897354478470176, "path": 8243487638840036315, "deps": [[1441306149310335789, "tempfile", false, 6507608586020284938], [1573238666360410412, "rand_chacha", false, 4326934058711142466], [4344686646292094751, "rusty_fork", false, 4903193783350036529], [5157631553186200874, "num_traits", false, 14567187325378532629], [5692597712387868707, "bit_vec", false, 16351780099768061045], [6166349630582887940, "bitflags", false, 1084317970753013230], [9408802513701742484, "regex_syntax", false, 5097873282249621720], [9519969280819313548, "bit_set", false, 11967599137472131269], [13208667028893622512, "rand", false, 6128791692759611846], [14014736296291115408, "unarray", false, 18114240408473789371], [16658906400288386541, "rand_xorshift", false, 8029589642952162486], [17917672826516349275, "lazy_static", false, 12891119196092674686]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/proptest-ba9dd3b8f10e31ee/dep-lib-proptest", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}