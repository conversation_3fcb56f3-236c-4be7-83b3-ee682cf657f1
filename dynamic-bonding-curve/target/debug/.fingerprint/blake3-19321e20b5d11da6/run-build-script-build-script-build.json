{"rustc": 16006996104574632743, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10093362315492047521, "build_script_build", false, 9912932868126476750]], "local": [{"RerunIfChanged": {"output": "debug/build/blake3-19321e20b5d11da6/output", "paths": ["c/main.c", "c/blake3_portable.c", "c/blake3_sse41_x86-64_windows_msvc.asm", "c/libblake3.pc.in", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/.giti<PERSON>re", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/test.py", "c/blake3.h", "c/blake3-config.cmake.in", "c/blake3_sse2_x86-64_windows_gnu.S", "c/blake3_avx2_x86-64_windows_gnu.S", "c/blake3_avx512.c", "c/blake3_neon.c", "c/blake3_sse2_x86-64_unix.S", "c/blake3_dispatch.c", "c/CMakePresets.json", "c/blake3_sse2.c", "c/README.md", "c/CMakeLists.txt", "c/Makefile.testing", "c/blake3.c", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/blake3_sse41_x86-64_unix.S", "c/blake3_sse41_x86-64_windows_gnu.S", "c/blake3_avx2.c", "c/blake3_sse41.c", "c/blake3_avx2_x86-64_unix.S", "c/blake3_impl.h", "c/blake3_avx512_x86-64_unix.S", "c/blake3_avx512_x86-64_windows_gnu.S", "c/example.c"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 0, "compile_kind": 0}