{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 12396604673175817722, "profile": 15657897354478470176, "path": 13086341560384249938, "deps": [[11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [12908127264913370629, "solana_curve25519", false, 18437850686004485897], [13077212702700853852, "base64", false, 12362641850911863679], [14074610438553418890, "bytemuck", false, 504061773874182866]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-ciphertext-arithmetic-275c0afc854e32d9/dep-lib-spl_token_confidential_transfer_ciphertext_arithmetic", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}