{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 8649083292250217093, "profile": 2225463790103693989, "path": 8269977690201355646, "deps": [[7343171641404694157, "syn", false, 9770868895459145944], [8164548788483266975, "spl_discriminator_syn", false, 16720047554600809415], [16437840124237027127, "quote", false, 890238129459630126]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-discriminator-derive-4dffd28959ae7f62/dep-lib-spl_discriminator_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}