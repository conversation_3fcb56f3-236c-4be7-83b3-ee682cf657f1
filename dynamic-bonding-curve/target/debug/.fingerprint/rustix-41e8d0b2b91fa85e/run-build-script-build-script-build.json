{"rustc": 16006996104574632743, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12053020504183902936, "build_script_build", false, 17512113018010325899]], "local": [{"RerunIfChanged": {"output": "debug/build/rustix-41e8d0b2b91fa85e/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 0, "compile_kind": 0}