{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[\"derive\"]", "target": 4936690485685825485, "profile": 15657897354478470176, "path": 9458134862375959805, "deps": [[761872526999005629, "solana_msg", false, 764072810218005281], [790673365560624081, "spl_pod", false, 1748297529695387651], [23*****************, "spl_discriminator", false, 18126927502500981551], [5157631553186200874, "num_traits", false, 14567187325378532629], [8008191657135824715, "thiserror", false, 8726492005281312749], [8904325910339146545, "solana_decode_error", false, 15870537064508360090], [11263754829263059703, "num_derive", false, 10303754624925241684], [11330212578939379604, "solana_program_error", false, 3336987160806504165], [14074610438553418890, "bytemuck", false, 504061773874182866], [16827618877557699677, "solana_account_info", false, 4807336393613611109]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-type-length-value-d6a4175e6d942a86/dep-lib-spl_type_length_value", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}