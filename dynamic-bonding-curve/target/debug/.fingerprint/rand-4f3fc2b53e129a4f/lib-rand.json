{"rustc": 16006996104574632743, "features": "[\"alloc\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 17988361328265503841, "deps": [[1333041802001714747, "rand_chacha", false, 1336110495807350364], [1740877332521282793, "rand_core", false, 17633786126084690420], [5170503507811329045, "getrandom_package", false, 7373864138430147011], [8194304432723500424, "libc", false, 11194284927636965423]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-4f3fc2b53e129a4f/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}