{"rustc": 16006996104574632743, "features": "[\"cargo_toml\", \"event-cpi\", \"hash\", \"idl-build\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"cargo_toml\", \"event-cpi\", \"hash\", \"idl-build\", \"init-if-needed\", \"interface-instructions\"]", "target": 17778334149744802995, "profile": 12878658482865712, "path": 16594463705896924593, "deps": [[2713742371683562785, "syn", false, 16295224067803507893], [3060637413840920116, "proc_macro2", false, 1276041253075378396], [5236433071915784494, "sha2", false, 3800606140697423872], [6170788409352141399, "cargo_toml", false, 16044002258996043413], [6616501577376279788, "bs58", false, 6489024754890322816], [8008191657135824715, "thiserror", false, 8726492005281312749], [9689903380558560274, "serde", false, 3711773370135953871], [15367738274754116744, "serde_json", false, 5360447862243250299], [16131248048418321657, "heck", false, 775254493768138390], [16437840124237027127, "quote", false, 890238129459630126], [18396545099218507036, "anyhow", false, 1697364170871226896]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-syn-d233e0f339b3c613/dep-lib-anchor_syn", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}