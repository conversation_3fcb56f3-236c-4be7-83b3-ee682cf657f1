{"rustc": 16006996104574632743, "features": "[\"bincode\", \"blake3\", \"serde\"]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 6652793396284126538, "path": 3072958864536961764, "deps": [[65234016722529558, "bincode", false, 8085518671288464072], [1461800729938538396, "solana_instruction", false, 7425754530109857019], [4113188218898653100, "solana_short_vec", false, 14649702869624120309], [7355047358885037824, "solana_hash", false, 12443036087723803351], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9556858120010252096, "solana_transaction_error", false, 9923603834631672204], [9689903380558560274, "serde", false, 3711773370135953871], [10093362315492047521, "blake3", false, 1399941729326479117], [11702702251883620295, "solana_bincode", false, 210931073118357241], [14591356476411885690, "solana_sdk_ids", false, 6479576252039189143], [15341883195918613377, "solana_system_interface", false, 3700732144194427837], [15429715045911386410, "solana_sanitize", false, 5311198693155383094], [16257276029081467297, "serde_derive", false, 13597335082114753276], [17917672826516349275, "lazy_static", false, 12891119196092674686]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-f741f46d7aac705e/dep-lib-solana_message", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}