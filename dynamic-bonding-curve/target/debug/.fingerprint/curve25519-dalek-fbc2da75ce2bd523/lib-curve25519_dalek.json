{"rustc": 16006996104574632743, "features": "[\"alloc\", \"default\", \"digest\", \"precomputed-tables\", \"rand_core\", \"serde\", \"zeroize\"]", "declared_features": "[\"alloc\", \"default\", \"digest\", \"ff\", \"group\", \"group-bits\", \"legacy_compatibility\", \"precomputed-tables\", \"rand_core\", \"serde\", \"zeroize\"]", "target": 115635582535548150, "profile": 15657897354478470176, "path": 9636083828732927599, "deps": [[1513171335889705703, "curve25519_dalek_derive", false, 11595825069670349066], [6528079939221783635, "zeroize", false, 11083344674282578726], [9689903380558560274, "serde", false, 3711773370135953871], [10411997081178400487, "cfg_if", false, 8227816264042300135], [13595581133353633439, "build_script_build", false, 3102437249569319511], [16728391542287073583, "cpufeatures", false, 8649484748525081933], [17003143334332120809, "subtle", false, 1424327540280269551], [17475753849556516473, "digest", false, 15149822100867769002], [18130209639506977569, "rand_core", false, 17103729651534988622]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-fbc2da75ce2bd523/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}