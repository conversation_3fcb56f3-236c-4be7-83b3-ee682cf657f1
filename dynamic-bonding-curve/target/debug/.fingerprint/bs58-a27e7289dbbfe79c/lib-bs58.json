{"rustc": 16006996104574632743, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 15657897354478470176, "path": 1990962021545512285, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bs58-a27e7289dbbfe79c/dep-lib-bs58", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}