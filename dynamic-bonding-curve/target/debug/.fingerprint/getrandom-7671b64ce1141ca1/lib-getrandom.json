{"rustc": 16006996104574632743, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 15657897354478470176, "path": 7707361407081820343, "deps": [[5170503507811329045, "build_script_build", false, 15187102819577610590], [8194304432723500424, "libc", false, 11194284927636965423], [10411997081178400487, "cfg_if", false, 8227816264042300135]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-7671b64ce1141ca1/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}