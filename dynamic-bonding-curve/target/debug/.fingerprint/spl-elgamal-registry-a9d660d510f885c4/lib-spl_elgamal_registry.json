{"rustc": 16006996104574632743, "features": "[\"no-entrypoint\"]", "declared_features": "[\"no-entrypoint\", \"test-sbf\"]", "target": 3313661393964375702, "profile": 6652793396284126538, "path": 9968870374329824567, "deps": [[790673365560624081, "spl_pod", false, 1748297529695387651], [11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [13155245355270259291, "spl_token_confidential_transfer_proof_extraction", false, 11581572431615790177], [14074610438553418890, "bytemuck", false, 504061773874182866], [16307125341177010248, "solana_program", false, 5495509304683080398]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-elgamal-registry-a9d660d510f885c4/dep-lib-spl_elgamal_registry", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}