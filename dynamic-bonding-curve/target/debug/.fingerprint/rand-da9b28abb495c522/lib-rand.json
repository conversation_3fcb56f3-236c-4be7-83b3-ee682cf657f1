{"rustc": 16006996104574632743, "features": "[\"alloc\", \"default\", \"os_rng\", \"small_rng\", \"std\", \"std_rng\", \"thread_rng\"]", "declared_features": "[\"alloc\", \"default\", \"log\", \"nightly\", \"os_rng\", \"serde\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\", \"thread_rng\", \"unbiased\"]", "target": 4488736914369465202, "profile": 15657897354478470176, "path": 9924009272719718958, "deps": [[5652558058897858086, "rand_chacha", false, 6529108029593737943], [13135315962794364551, "rand_core", false, 9467780426485317666]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-da9b28abb495c522/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}