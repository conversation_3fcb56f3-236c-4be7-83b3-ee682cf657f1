{"rustc": 16006996104574632743, "features": "[\"bincode\", \"serde\"]", "declared_features": "[\"bincode\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 3360161622638113394, "profile": 6652793396284126538, "path": 6524095664258167876, "deps": [[1461800729938538396, "solana_instruction", false, 7425754530109857019], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9689903380558560274, "serde", false, 3711773370135953871], [12201281988255911495, "serde_bytes", false, 10678696138346802588], [14591356476411885690, "solana_sdk_ids", false, 6479576252039189143], [15341883195918613377, "solana_system_interface", false, 3700732144194427837], [16257276029081467297, "serde_derive", false, 13597335082114753276]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-loader-v3-interface-6352b82bbb17d436/dep-lib-solana_loader_v3_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}