{"rustc": 16006996104574632743, "features": "[\"default\", \"num-bigint\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"libm\", \"num-bigint\", \"rand\", \"serde\", \"std\"]", "target": 10053607161131906775, "profile": 15657897354478470176, "path": 7279905320657584001, "deps": [[2819946551904607991, "num_rational", false, 785211091876720795], [5157631553186200874, "num_traits", false, 14567187325378532629], [5666221976914082401, "num_iter", false, 7116720655727799520], [12319020793864570031, "num_complex", false, 12935593119800962288], [12528732512569713347, "num_bigint", false, 4255158075484344370], [16795989132585092538, "num_integer", false, 11987322723058567609]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/num-d55f2c9cbcdd4299/dep-lib-num", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}