{"rustc": 16006996104574632743, "features": "[\"borsh-derive\", \"default\", \"derive\", \"std\", \"unstable__schema\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"rc\", \"std\", \"unstable__schema\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 11971765316808316758, "deps": [[1884099982326826527, "cfg_aliases", false, 7752143954805180970]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-91a6f0886f82ed9f/dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}