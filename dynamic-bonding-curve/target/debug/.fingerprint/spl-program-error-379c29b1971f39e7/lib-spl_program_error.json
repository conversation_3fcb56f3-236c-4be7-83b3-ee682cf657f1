{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 3267741926779996941, "profile": 15657897354478470176, "path": 3045330802690386647, "deps": [[667922753503464778, "spl_program_error_derive", false, 15249589943998781430], [5157631553186200874, "num_traits", false, 14567187325378532629], [8008191657135824715, "thiserror", false, 8726492005281312749], [11263754829263059703, "num_derive", false, 10303754624925241684], [16307125341177010248, "solana_program", false, 5495509304683080398]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-program-error-379c29b1971f39e7/dep-lib-spl_program_error", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}