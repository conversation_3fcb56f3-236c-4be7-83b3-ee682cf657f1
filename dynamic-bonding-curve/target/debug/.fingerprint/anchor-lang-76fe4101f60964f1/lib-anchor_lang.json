{"rustc": 16006996104574632743, "features": "[\"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\", \"interface-instructions\", \"lazy-account\"]", "target": 14695202496702424983, "profile": 15657897354478470176, "path": 8112575430636988558, "deps": [[*****************, "bincode", false, 8085518671288464072], [1205890147656972920, "anchor_derive_accounts", false, 11998890479493566438], [3427083074141588066, "base64", false, 8903979636505909883], [4419926601033140305, "borsh", false, 11154140471036942712], [5532297295134723458, "anchor_attribute_program", false, 17188377430123656413], [6080285880102702883, "anchor_derive_space", false, 3994669015116908537], [8008191657135824715, "thiserror", false, 8726492005281312749], [10220848352499156513, "anchor_lang_idl", false, 5424562905797118320], [10784666044722074209, "anchor_attribute_event", false, 14157098017553324019], [11737296378741312020, "anchor_attribute_account", false, 8337404211472000169], [13139933692030873282, "anchor_attribute_error", false, 10532823048083250479], [13977390777787220484, "anchor_derive_serde", false, 13027935991995598218], [14074610438553418890, "bytemuck", false, 504061773874182866], [14555048766774983064, "anchor_attribute_constant", false, 9756458566147820194], [16070395273854428984, "anchor_attribute_access_control", false, 12463783331712182724], [16307125341177010248, "solana_program", false, 5495509304683080398]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-76fe4101f60964f1/dep-lib-anchor_lang", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}