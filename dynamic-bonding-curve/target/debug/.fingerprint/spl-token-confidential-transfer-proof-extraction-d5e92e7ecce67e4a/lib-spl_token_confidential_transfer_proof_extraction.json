{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 9070344044293252602, "profile": 15657897354478470176, "path": 268060750402318527, "deps": [[790673365560624081, "spl_pod", false, 1748297529695387651], [10806645703491011684, "thiserror", false, 4716163417999155942], [11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [12908127264913370629, "solana_curve25519", false, 18437850686004485897], [14074610438553418890, "bytemuck", false, 504061773874182866], [16307125341177010248, "solana_program", false, 5495509304683080398]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-proof-extraction-d5e92e7ecce67e4a/dep-lib-spl_token_confidential_transfer_proof_extraction", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}