{"rustc": 16006996104574632743, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8475749685358207080, "path": 17225201332212744863, "deps": [[6166349630582887940, "bitflags", false, 1084317970753013230], [12053020504183902936, "build_script_build", false, 3541668216823330137], [12846346674781677812, "linux_raw_sys", false, 6816684299370623057]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-956183a8f80e0929/dep-lib-rustix", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}