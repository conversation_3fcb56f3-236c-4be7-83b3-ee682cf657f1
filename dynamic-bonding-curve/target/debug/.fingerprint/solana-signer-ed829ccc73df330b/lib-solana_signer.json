{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 1643282199907566603, "profile": 15657897354478470176, "path": 13532099282891615640, "deps": [[9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9556858120010252096, "solana_transaction_error", false, 9923603834631672204], [15632274837287148962, "solana_signature", false, 10663583686805963329]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-signer-ed829ccc73df330b/dep-lib-solana_signer", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}