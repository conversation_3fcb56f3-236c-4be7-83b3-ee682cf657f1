{"rustc": 16006996104574632743, "features": "[\"serde\"]", "declared_features": "[\"dev-context-only-utils\", \"serde\"]", "target": 10340454008857965518, "profile": 15657897354478470176, "path": 17289217746065251336, "deps": [[7355047358885037824, "solana_hash", false, 12443036087723803351], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9689903380558560274, "serde", false, 3711773370135953871], [11104455582174147483, "solana_sha256_hasher", false, 9310088915920486603], [16257276029081467297, "serde_derive", false, 13597335082114753276], [17802518109446470116, "solana_fee_calculator", false, 9179697194060979239]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-nonce-22ff8ec4518898ae/dep-lib-solana_nonce", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}