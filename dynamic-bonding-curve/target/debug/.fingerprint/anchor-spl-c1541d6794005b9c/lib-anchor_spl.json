{"rustc": 16006996104574632743, "features": "[\"associated_token\", \"default\", \"idl-build\", \"mint\", \"spl-associated-token-account\", \"spl-pod\", \"spl-token\", \"spl-token-2022\", \"spl-token-group-interface\", \"spl-token-metadata-interface\", \"token\", \"token_2022\", \"token_2022_extensions\"]", "declared_features": "[\"anchor-debug\", \"associated_token\", \"borsh\", \"default\", \"devnet\", \"governance\", \"idl-build\", \"memo\", \"metadata\", \"mint\", \"mpl-token-metadata\", \"spl-associated-token-account\", \"spl-memo\", \"spl-pod\", \"spl-token\", \"spl-token-2022\", \"spl-token-group-interface\", \"spl-token-metadata-interface\", \"stake\", \"token\", \"token_2022\", \"token_2022_extensions\"]", "target": 9008755946677022642, "profile": 15657897354478470176, "path": 15990397605882369902, "deps": [[790673365560624081, "spl_pod", false, 1748297529695387651], [9413657500826327667, "spl_token_2022", false, 17491525392374944298], [10497244395353946307, "spl_associated_token_account", false, 700545340239244631], [12682673687743740477, "spl_token", false, 2933221618394261003], [16226330934343841514, "anchor_lang", false, 13001347573110559819], [17340930586486050809, "spl_token_group_interface", false, 14483656829555358562], [17667569856882013889, "spl_token_metadata_interface", false, 14571336715540097125]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-spl-c1541d6794005b9c/dep-lib-anchor_spl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}