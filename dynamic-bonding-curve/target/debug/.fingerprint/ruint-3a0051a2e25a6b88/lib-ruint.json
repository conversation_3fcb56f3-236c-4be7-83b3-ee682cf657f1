{"rustc": 16006996104574632743, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"alloy-rlp\", \"arbitrary\", \"ark-ff\", \"ark-ff-04\", \"bn-rs\", \"borsh\", \"bytemuck\", \"default\", \"der\", \"diesel\", \"fastrlp\", \"fastrlp-04\", \"generic_const_exprs\", \"nightly\", \"num-bigint\", \"num-integer\", \"num-traits\", \"parity-scale-codec\", \"postgres\", \"primitive-types\", \"proptest\", \"pyo3\", \"quickcheck\", \"rand\", \"rand-09\", \"rlp\", \"serde\", \"sqlx\", \"ssz\", \"std\", \"subtle\", \"valuable\", \"zeroize\"]", "target": 3022186276765814770, "profile": 15657897354478470176, "path": 7085868137716303180, "deps": [[5084698891385807815, "ruint_macro", false, 8424461557354952579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ruint-3a0051a2e25a6b88/dep-lib-ruint", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}