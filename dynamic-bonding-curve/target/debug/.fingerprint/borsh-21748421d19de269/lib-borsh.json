{"rustc": 16006996104574632743, "features": "[\"borsh-derive\", \"default\", \"derive\", \"std\", \"unstable__schema\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"rc\", \"std\", \"unstable__schema\"]", "target": 4760962088884618199, "profile": 15657897354478470176, "path": 12224615416563411485, "deps": [[7718779164700081417, "build_script_build", false, 11196387123296161506], [13728209559156011990, "borsh_derive", false, 943318568211412948]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-21748421d19de269/dep-lib-borsh", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}