{"rustc": 16006996104574632743, "features": "[\"sha2\", \"static-context\", \"std\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 15657897354478470176, "path": 3101060889256286799, "deps": [[4731167174326621189, "rand", false, 16334807171889637164], [6374421995994392543, "digest", false, 16834107040630345039], [9529943735784919782, "arrayref", false, 8718197258988919556], [9689903380558560274, "serde", false, 3711773370135953871], [10697153736615144157, "build_script_build", false, 4542072900383038137], [11472355562936271783, "sha2", false, 6984016187123985380], [13443824959912985638, "libsecp256k1_core", false, 4278134101632483662], [17072468807347166763, "base64", false, 10977269676244710686]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsecp256k1-c2bab7cd70fce8a9/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}