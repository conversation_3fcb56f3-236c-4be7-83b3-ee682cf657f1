{"rustc": 16006996104574632743, "features": "[\"bincode\", \"bytemuck\", \"serde\"]", "declared_features": "[\"bincode\", \"bytemuck\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 16616187019842477339, "profile": 6652793396284126538, "path": 12679941438846126433, "deps": [[65234016722529558, "bincode", false, 8085518671288464072], [1461800729938538396, "solana_instruction", false, 7425754530109857019], [9475981483390999469, "solana_pubkey", false, 8221103193541484978], [9689903380558560274, "serde", false, 3711773370135953871], [12564212015386640564, "solana_clock", false, 17042852909674836676], [14074610438553418890, "bytemuck", false, 504061773874182866], [14591356476411885690, "solana_sdk_ids", false, 6479576252039189143], [16257276029081467297, "serde_derive", false, 13597335082114753276], [16847021361644352524, "solana_slot_hashes", false, 10001214221822530459]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-address-lookup-table-interface-0c9b11aee98895ed/dep-lib-solana_address_lookup_table_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}