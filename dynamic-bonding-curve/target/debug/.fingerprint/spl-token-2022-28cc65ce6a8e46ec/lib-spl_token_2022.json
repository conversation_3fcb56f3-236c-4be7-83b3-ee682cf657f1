{"rustc": 16006996104574632743, "features": "[\"default\", \"no-entrypoint\", \"zk-ops\"]", "declared_features": "[\"default\", \"no-entrypoint\", \"serde-traits\", \"test-sbf\", \"zk-ops\"]", "target": 2664260296053320942, "profile": 6652793396284126538, "path": 9312118733824841980, "deps": [[790673365560624081, "spl_pod", false, 1748297529695387651], [1470679118034951355, "num_enum", false, 9743762361045892160], [5157631553186200874, "num_traits", false, 14567187325378532629], [7862880122426466754, "spl_elgamal_registry", false, 1009103520682235449], [8008191657135824715, "thiserror", false, 8726492005281312749], [9453905076908448987, "spl_token_confidential_transfer_proof_generation", false, 2773594100016177839], [9529943735784919782, "arrayref", false, 8718197258988919556], [9986636203687404999, "spl_transfer_hook_interface", false, 1202799638051883710], [11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [11263754829263059703, "num_derive", false, 10303754624925241684], [11885291781139204510, "spl_memo", false, 4333442218251838600], [12682673687743740477, "spl_token", false, 2933221618394261003], [13155245355270259291, "spl_token_confidential_transfer_proof_extraction", false, 11581572431615790177], [13937246957939922657, "spl_type_length_value", false, 17692783773248048158], [14074610438553418890, "bytemuck", false, 504061773874182866], [16307125341177010248, "solana_program", false, 5495509304683080398], [17340930586486050809, "spl_token_group_interface", false, 14483656829555358562], [17667569856882013889, "spl_token_metadata_interface", false, 14571336715540097125], [17899936559789965257, "spl_token_confidential_transfer_ciphertext_arithmetic", false, 11865521077468276838], [17909568817133603617, "solana_security_txt", false, 5771515339604835695]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-2022-28cc65ce6a8e46ec/dep-lib-spl_token_2022", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}