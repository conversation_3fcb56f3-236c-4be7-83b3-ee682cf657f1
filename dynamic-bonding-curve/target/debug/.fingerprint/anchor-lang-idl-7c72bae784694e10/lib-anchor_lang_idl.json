{"rustc": 16006996104574632743, "features": "[\"build\", \"regex\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 15657897354478470176, "path": 4152399251661079441, "deps": [[9451456094439810778, "regex", false, 2850256005766220289], [9689903380558560274, "serde", false, 3711773370135953871], [15367738274754116744, "serde_json", false, 5360447862243250299], [17037804673887881428, "anchor_lang_idl_spec", false, 1016809099000504103], [18396545099218507036, "anyhow", false, 1697364170871226896]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-idl-7c72bae784694e10/dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}