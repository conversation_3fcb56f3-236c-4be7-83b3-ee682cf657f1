{"rustc": 16006996104574632743, "features": "[]", "declared_features": "[]", "target": 1817782919825969750, "profile": 6652793396284126538, "path": 824877643155020966, "deps": [[8008191657135824715, "thiserror", false, 8726492005281312749], [11000099544314642151, "solana_zk_sdk", false, 2866297757136188431], [13595581133353633439, "curve25519_dalek", false, 8106951365886900196]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-confidential-transfer-proof-generation-0f5c168196e864f9/dep-lib-spl_token_confidential_transfer_proof_generation", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}