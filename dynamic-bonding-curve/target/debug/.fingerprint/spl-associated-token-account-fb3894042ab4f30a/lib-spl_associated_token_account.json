{"rustc": 16006996104574632743, "features": "[\"no-entrypoint\"]", "declared_features": "[\"no-entrypoint\", \"test-sbf\"]", "target": 6203156852457943672, "profile": 15657897354478470176, "path": 17324455978178350387, "deps": [[5157631553186200874, "num_traits", false, 14567187325378532629], [7718779164700081417, "borsh", false, *****************], [8008191657135824715, "thiserror", false, 8726492005281312749], [9413657500826327667, "spl_token_2022", false, 17491525392374944298], [11263754829263059703, "num_derive", false, 10303754624925241684], [11304091895982676788, "spl_associated_token_account_client", false, 14280293865450763147], [12682673687743740477, "spl_token", false, 2933221618394261003], [16307125341177010248, "solana_program", false, 5495509304683080398]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-associated-token-account-fb3894042ab4f30a/dep-lib-spl_associated_token_account", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}